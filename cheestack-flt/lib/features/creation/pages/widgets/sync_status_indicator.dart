import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../controllers/creation_controller.dart';

/// 同步状态指示器组件
/// 显示同步进度、状态文字、最后同步时间和手动同步按钮
class SyncStatusIndicator extends StatelessWidget {
  const SyncStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (controller) {
        final syncState = controller.syncState;

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: _getStatusColor(syncState.status, context)
                .withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: _getStatusColor(syncState.status, context)
                  .withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // 状态图标
              _buildStatusIcon(syncState.status, context),
              SizedBox(width: 8.w),

              // 状态信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 状态文字
                    Text(
                      _getStatusText(syncState.status),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(syncState.status, context),
                      ),
                    ),

                    // 进度条（同步中时显示）
                    if (syncState.status == SyncStatus.syncing) ...[
                      SizedBox(height: 4.h),
                      LinearProgressIndicator(
                        value: syncState.progress,
                        backgroundColor: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getStatusColor(syncState.status, context),
                        ),
                      ),
                    ],

                    // 最后同步时间
                    if (syncState.lastSyncTime != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        '最后同步: ${_formatSyncTime(syncState.lastSyncTime!)}',
                        style: TextStyle(
                          fontSize: 12.sp,
                        ),
                      ),
                    ],

                    // 错误信息
                    if (syncState.status == SyncStatus.failed &&
                        syncState.message != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        syncState.message!,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // 手动同步按钮
              if (syncState.status != SyncStatus.syncing)
                IconButton(
                  onPressed: () => controller.syncToCloud(),
                  icon: Icon(
                    Icons.sync,
                    size: 20.sp,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  tooltip: '手动同步',
                ),
            ],
          ),
        );
      },
    );
  }

  /// 获取状态图标
  Widget _buildStatusIcon(SyncStatus status, BuildContext context) {
    switch (status) {
      case SyncStatus.idle:
        return Icon(
          Icons.cloud_off_outlined,
          size: 20.sp,
        );
      case SyncStatus.syncing:
        return SizedBox(
          width: 20.sp,
          height: 20.sp,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        );
      case SyncStatus.success:
        return Icon(
          Icons.cloud_done_outlined,
          size: 20.sp,
          color: Theme.of(context).colorScheme.primary,
        );
      case SyncStatus.failed:
        return Icon(
          Icons.cloud_off_outlined,
          size: 20.sp,
          color: Theme.of(context).colorScheme.error,
        );
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(SyncStatus status, BuildContext context) {
    switch (status) {
      case SyncStatus.idle:
        return Theme.of(context).colorScheme.onSurfaceVariant;
      case SyncStatus.syncing:
        return Theme.of(context).colorScheme.primary;
      case SyncStatus.success:
        return Theme.of(context).colorScheme.primary;
      case SyncStatus.failed:
        return Theme.of(context).colorScheme.error;
    }
  }

  /// 获取状态文字
  String _getStatusText(SyncStatus status) {
    switch (status) {
      case SyncStatus.idle:
        return '离线模式';
      case SyncStatus.syncing:
        return '正在同步...';
      case SyncStatus.success:
        return '同步成功';
      case SyncStatus.failed:
        return '同步失败';
    }
  }

  /// 格式化同步时间
  String _formatSyncTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inHours < 24) {
      return '${diff.inHours}小时前';
    } else {
      return '${diff.inDays}天前';
    }
  }
}
