import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/shared/utils/index.dart';

void main() {
  group('COS工具测试', () {
    test('Scos应该有正确的配置', () {
      final scos = OxCos();

      expect(scos.bucket, 'cheestack-1314772501');
      expect(scos.region, 'ap-guangzhou');
      expect(scos.baseUrl,
          'https://cheestack-1314772501.cos.ap-guangzhou.myqcloud.com');
    });

    test('应该能够生成正确的COS路径格式', () {
      final scos = OxCos();

      // 模拟用户ID
      const testUserId = 'test-user-123';

      // 由于getKey方法依赖AuthController，我们测试路径格式
      final testPath = '/test/image.jpg';
      final filename = 'avatar_test';

      // 验证路径组件
      expect(testPath.split('/').last, 'image.jpg');
      expect(filename, 'avatar_test');
      expect(UploadFileType.image.name, 'image');
    });

    test('UploadFileType枚举应该有正确的值', () {
      expect(UploadFileType.image.name, 'image');
      expect(UploadFileType.audio.name, 'audio');
      expect(UploadFileType.viedo.name, 'viedo'); // 注意：这里是viedo不是video
      expect(UploadFileType.file.name, 'file');
    });
  });
}
